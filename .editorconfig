# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# TypeScript and JavaScript files
[*.{ts,js,tsx,jsx}]
indent_style = space
indent_size = 2

# JSON files
[*.json]
indent_style = space
indent_size = 2

# HTML files
[*.html]
indent_style = space
indent_size = 2

# CSS files
[*.{css,scss,sass}]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false

# Package files
[package.json]
indent_style = space
indent_size = 2