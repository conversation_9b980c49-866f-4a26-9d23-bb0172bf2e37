name: CI

# 触发条件：推送到主分支或创建Pull Request
on:
  push:
    branches: [main, master]
  pull_request:
    branches: [main, master]

jobs:
  # 代码质量检查任务
  quality-check:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
      # 检出代码
      - name: 检出代码
        uses: actions/checkout@v4

      # 设置 Node.js 环境
      - name: 设置 Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      # 设置 pnpm
      - name: 设置 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest

      # 获取 pnpm 缓存目录
      - name: 获取 pnpm 缓存目录
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      # 设置 pnpm 缓存
      - name: 设置 pnpm 缓存
        uses: actions/cache@v3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      # 安装依赖
      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      # TypeScript 类型检查
      - name: TypeScript 类型检查
        run: pnpm run type-check

      # ESLint 代码检查
      - name: ESLint 代码检查
        run: pnpm run lint

      # Prettier 格式检查
      - name: Prettier 格式检查
        run: pnpm run format:check

      # 构建项目
      - name: 构建项目
        run: pnpm run build
