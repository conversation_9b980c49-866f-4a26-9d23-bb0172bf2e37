# Lit + UnoCSS 项目

这是一个使用 **Lit 3.3.0** 和 **UnoCSS** 构建的现代 Web Components 项目。

## 🚀 技术栈

- **Lit 3.3.0** - 轻量级 Web Components 框架
- **UnoCSS** - 即时原子化 CSS 引擎
- **TypeScript** - 类型安全的 JavaScript
- **Vite** - 快速的前端构建工具
- **pnpm** - 高效的包管理器

## 📦 安装依赖

```bash
pnpm install
```

## 🛠️ 开发

启动开发服务器：

```bash
pnpm dev
```

访问 `http://localhost:3000` 查看项目。

## 🏗️ 构建

构建生产版本：

```bash
pnpm build
```

预览构建结果：

```bash
pnpm preview
```

## 🎨 UnoCSS 配置

### 预设

项目使用了以下 UnoCSS 预设：

- `presetUno()` - 默认预设，兼容 Tailwind CSS
- `presetAttributify()` - 属性化模式
- `presetIcons()` - 图标支持

### 自定义快捷方式

在 `uno.config.ts` 中定义了以下快捷方式：

```typescript
shortcuts: {
  'btn': 'px-4 py-2 rounded bg-primary text-white hover:bg-blue-600 transition-colors',
  'card': 'p-6 bg-white rounded-xl shadow-lg',
}
```

### 使用方式

#### 1. 传统类名方式

```html
<div class="p-4 bg-blue-500 text-white rounded-lg">
  传统方式
</div>
```

#### 2. 属性化模式

```html
<div 
  p="4" 
  bg="blue-500" 
  text="white" 
  rounded="lg"
>
  属性化模式
</div>
```

#### 3. 自定义快捷方式

```html
<button class="btn">按钮</button>
<div class="card">卡片内容</div>
```

## 📁 项目结构

```
src/
├── components/
│   ├── my-element.ts      # 原始示例组件
│   └── unocss-demo.ts     # UnoCSS 功能演示组件
├── styles/
│   └── index.css          # 基础样式
├── assets/
│   └── lit.svg           # 资源文件
└── index.ts              # 入口文件
```

## 🔧 组件开发

### 创建新组件

```typescript
import { LitElement, html, css } from 'lit'
import { customElement, property } from 'lit/decorators.js'

@customElement('my-component')
export class MyComponent extends LitElement {
  @property()
  title = 'Hello UnoCSS'

  protected render() {
    return html`
      <div class="p-6 bg-white rounded-lg shadow-md">
        <h1 class="text-2xl font-bold text-blue-600">${this.title}</h1>
        <p class="mt-2 text-gray-600">使用 UnoCSS 样式</p>
      </div>
    `
  }

  static styles = css`
    :host {
      display: block;
    }
  `
}
```

### 在 HTML 中使用

```html
<my-component title="自定义标题"></my-component>
```

## 🎯 UnoCSS 优势

1. **即时生成** - 只生成实际使用的 CSS
2. **高性能** - 比传统 CSS 框架更快
3. **灵活配置** - 支持自定义规则和预设
4. **多种模式** - 支持类名和属性化两种写法
5. **TypeScript 支持** - 完整的类型提示

## 📚 相关文档

- [Lit 官方文档](https://lit.dev/)
- [UnoCSS 官方文档](https://unocss.dev/)
- [Vite 官方文档](https://vitejs.dev/)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License