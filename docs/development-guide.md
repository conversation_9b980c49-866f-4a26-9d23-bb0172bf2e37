# 开发指南

本文档介绍了项目的开发工具配置和最佳实践。

## 🛠️ 开发工具

### ESLint
- **配置文件**: `eslint.config.js`
- **功能**: 代码质量检查和规范约束
- **支持**: TypeScript、Lit 组件、现代 JavaScript

### Prettier
- **配置文件**: `.prettierrc.json`
- **功能**: 代码格式化
- **忽略文件**: `.prettierignore`

### EditorConfig
- **配置文件**: `.editorconfig`
- **功能**: 跨编辑器的代码风格一致性

### Husky + lint-staged
- **功能**: Git 钩子，提交前自动检查代码
- **配置**: `package.json` 中的 `lint-staged` 字段

## 📝 可用脚本

```bash
# 开发服务器
pnpm run dev

# 构建项目
pnpm run build

# 预览构建结果
pnpm run preview

# 代码检查
pnpm run lint

# 自动修复代码问题
pnpm run lint:fix

# 格式化代码
pnpm run format

# 检查代码格式
pnpm run format:check

# TypeScript 类型检查
pnpm run type-check
```

## 🚀 开发流程

### 1. 环境设置
```bash
# 安装依赖
pnpm install

# 初始化 Git 钩子
pnpm run prepare
```

### 2. 日常开发
1. 启动开发服务器：`pnpm run dev`
2. 编写代码时，IDE 会自动格式化和提示错误
3. 提交前会自动运行代码检查

### 3. 代码提交
```bash
# 添加文件
git add .

# 提交（会自动运行 pre-commit 钩子）
git commit -m "feat: 添加新功能"
```

## 🎯 代码规范

### TypeScript/JavaScript
- 使用 2 个空格缩进
- 使用单引号
- 行末添加分号
- 最大行长度 100 字符
- 使用 ES6+ 语法

### Lit 组件
- 遵循 Lit 官方最佳实践
- 使用装饰器语法
- 正确处理属性和事件

### 文件命名
- 组件文件：`kebab-case.ts`
- 工具函数：`camelCase.ts`
- 常量文件：`UPPER_CASE.ts`

## 🔧 IDE 配置

### VS Code（推荐）
项目已包含 `.vscode/settings.json` 配置：
- 保存时自动格式化
- ESLint 自动修复
- TypeScript 智能提示

### 推荐扩展
- ESLint
- Prettier - Code formatter
- TypeScript Importer
- Lit Plugin

## 🚨 常见问题

### ESLint 错误
```bash
# 查看所有错误
pnpm run lint

# 自动修复可修复的错误
pnpm run lint:fix
```

### 格式化问题
```bash
# 格式化所有文件
pnpm run format

# 检查格式是否正确
pnpm run format:check
```

### TypeScript 错误
```bash
# 运行类型检查
pnpm run type-check
```

### Git 钩子问题
如果 pre-commit 钩子失败：
1. 修复报告的错误
2. 重新添加文件：`git add .`
3. 重新提交：`git commit -m "your message"`

## 📚 最佳实践

### 1. 代码组织
- 按功能模块组织文件
- 使用 barrel exports（index.ts）
- 保持文件小而专注

### 2. 类型安全
- 启用严格的 TypeScript 检查
- 为所有公共 API 添加类型注解
- 避免使用 `any` 类型

### 3. 性能优化
- 使用 Lit 的响应式属性
- 避免不必要的重新渲染
- 合理使用 CSS 自定义属性

### 4. 测试
- 为组件编写单元测试
- 使用 Web Test Runner
- 保持高测试覆盖率

## 🔄 CI/CD

项目配置了 GitHub Actions 工作流（`.github/workflows/ci.yml`）：
- 自动运行代码检查
- 多 Node.js 版本测试
- 构建验证

每次推送到主分支或创建 Pull Request 时都会触发检查。