{"name": "litcdc", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .ts,.js,.tsx,.jsx", "lint:fix": "eslint . --ext .ts,.js,.tsx,.jsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "prepare": "husky install", "pre-commit": "lint-staged"}, "dependencies": {"lit": "^3.3.0", "vite-plugin-inspect": "^11.3.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^24.0.10", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-lit": "^2.1.1", "eslint-plugin-prettier": "^5.5.1", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "3.6.2", "typescript": "~5.8.3", "unocss": "^66.3.2", "vite": "^7.0.1"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}