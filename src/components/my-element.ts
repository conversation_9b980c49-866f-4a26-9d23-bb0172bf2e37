import { LitElement, html, unsafeCSS } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import litLogo from '../assets/lit.svg';
import viteLogo from '/vite.svg';
import style from '../styles/index.css?inline';

// 定义包含 UnoCSS 占位符的样式
const componentStyles = `

  @unocss-placeholder
`;

/**
 * An example element.
 *
 * @slot - This element has a slot
 * @csspart button - The button
 */
@customElement('my-element')
export class MyElement extends LitElement {
  /**
   * Copy for the read the docs hint.
   */
  @property()
  docsHint = 'Click on the Vite and Lit logos to learn more';

  /**
   * The number of times the button has been clicked.
   */
  @property({ type: Number })
  count = 0;

  protected render() {
    return html`
      <div class="p-8 space-y-6">
        <!-- UnoCSS 测试区域 -->
        <div
          class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-lg shadow-xl"
        >
          <h2 class="text-2xl font-bold mb-4">🎨 UnoCSS 测试组件</h2>
          <p class="text-blue-100">测试 UnoCSS 是否在 Shadow DOM 中正常工作</p>
        </div>

        <!-- 颜色和背景测试 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="bg-red-500 text-white p-4 rounded-lg text-center">
            <h3 class="font-semibold">红色背景</h3>
            <p class="text-red-100">bg-red-500</p>
          </div>
          <div class="bg-green-500 text-white p-4 rounded-lg text-center">
            <h3 class="font-semibold">绿色背景</h3>
            <p class="text-green-100">bg-green-500</p>
          </div>
          <div class="bg-yellow-500 text-black p-4 rounded-lg text-center">
            <h3 class="font-semibold">黄色背景</h3>
            <p class="text-yellow-800">bg-yellow-500</p>
          </div>
        </div>

        <!-- 自定义快捷方式测试 -->
        <div class="space-y-4">
          <h3 class="text-xl font-bold text-gray-800">自定义快捷方式测试</h3>
          <div class="flex gap-4 flex-wrap">
            <div class="cool-blue p-4 rounded-lg">Cool Blue 快捷方式</div>
            <div class="cool-green p-4 rounded-lg">Cool Green 快捷方式</div>
            <div class="logo text-6xl">Logo 图标</div>
          </div>
        </div>

        <!-- 原有内容 -->
        <div class="border-t pt-6">
          <div
            class="mx-auto flex max-w-sm items-center gap-x-4 rounded-xl bg-white p-6 shadow-lg outline outline-black/5 dark:bg-slate-800 dark:shadow-none dark:-outline-offset-1 dark:outline-white/10"
          >
            <img class="w-12 h-12 shrink-0" src=${litLogo} alt="ChitChat Logo" />
            <div>
              <div class="text-xl font-medium text-black dark:text-white">ChitChat</div>
              <p class="text-gray-500 dark:text-gray-400">You have a new message!</p>
            </div>
          </div>

          <h1 class="text-3xl font-bold underline mt-6">Hello world!</h1>

          <div class="flex justify-center gap-4 mt-4">
            <a href="https://vite.dev" target="_blank" class="hover:scale-110 transition-transform">
              <img src=${viteLogo} class="logo w-16 h-16" alt="Vite logo" />
            </a>
            <a href="https://lit.dev" target="_blank" class="hover:scale-110 transition-transform">
              <img src=${litLogo} class="logo lit w-16 h-16" alt="Lit logo" />
            </a>
          </div>
        </div>

        <slot></slot>

        <!-- 按钮测试 -->
        <div class="bg-gray-50 p-6 rounded-lg">
          <h3 class="text-lg font-semibold mb-4">交互测试</h3>
          <div class="flex gap-4 flex-wrap">
            <button
              @click=${this._onClick}
              part="button"
              class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors active:scale-95 transform"
            >
              点击计数: ${this.count}
            </button>
            <button
              class="bg-gradient-to-r from-pink-500 to-violet-500 text-white px-6 py-2 rounded-lg hover:shadow-lg transition-shadow"
            >
              渐变按钮
            </button>
            <button
              class="border-2 border-gray-300 hover:border-gray-500 px-6 py-2 rounded-lg transition-colors"
            >
              边框按钮
            </button>
          </div>
        </div>

        <!-- 响应式测试 -->
        <div class="bg-indigo-50 p-6 rounded-lg">
          <h3 class="text-lg font-semibold mb-4">响应式测试</h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-indigo-500 text-white p-4 rounded text-center">手机</div>
            <div class="bg-indigo-600 text-white p-4 rounded text-center hidden sm:block">
              平板+
            </div>
            <div class="bg-indigo-700 text-white p-4 rounded text-center hidden lg:block">
              桌面+
            </div>
            <div class="bg-indigo-800 text-white p-4 rounded text-center hidden lg:block">
              大屏+
            </div>
          </div>
        </div>

        <p class="read-the-docs text-center text-gray-600">${this.docsHint}</p>
      </div>
    `;
  }

  private _onClick() {
    this.count++;
  }

  static styles = [unsafeCSS(style), unsafeCSS(componentStyles)];
}

declare global {
  interface HTMLElementTagNameMap {
    'my-element': MyElement;
  }
}
