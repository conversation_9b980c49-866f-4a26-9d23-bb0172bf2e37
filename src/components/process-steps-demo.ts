import { LitElement, html, css } from 'lit';
import { customElement, state } from 'lit/decorators.js';
import { ProcessStep, StepStatus, StepClickEvent } from './process-steps.js';
import './process-steps.js';

/**
 * ProcessSteps 组件演示
 * 展示各种配置选项和使用场景
 */
@customElement('process-steps-demo')
export class ProcessStepsDemo extends LitElement {
  /**
   * 基础流程步骤数据
   */
  @state()
  private basicSteps: ProcessStep[] = [
    {
      id: 'step1',
      title: '提交申请',
      description: '填写并提交申请表单',
      timestamp: new Date('2024-01-15T09:00:00'),
    },
    {
      id: 'step2',
      title: '审核中',
      description: '相关部门正在审核您的申请',
      timestamp: new Date('2024-01-15T14:30:00'),
    },
    {
      id: 'step3',
      title: '审核通过',
      description: '申请已通过审核，准备处理',
    },
    {
      id: 'step4',
      title: '处理完成',
      description: '申请处理完成，可以查看结果',
    },
  ];

  /**
   * 自定义状态的步骤数据
   */
  @state()
  private customStatusSteps: ProcessStep[] = [
    {
      id: 'order1',
      title: '订单确认',
      description: '订单已确认，准备发货',
      status: StepStatus.COMPLETED,
      timestamp: new Date('2024-01-20T10:00:00'),
    },
    {
      id: 'order2',
      title: '商品出库',
      description: '商品正在打包出库',
      status: StepStatus.PROCESSING,
      timestamp: new Date('2024-01-20T15:30:00'),
    },
    {
      id: 'order3',
      title: '物流配送',
      description: '商品已交付物流公司配送',
      status: StepStatus.PENDING,
    },
    {
      id: 'order4',
      title: '签收完成',
      description: '客户签收，订单完成',
      status: StepStatus.PENDING,
    },
  ];

  /**
   * 可点击的步骤数据
   */
  @state()
  private clickableSteps: ProcessStep[] = [
    {
      id: 'tutorial1',
      title: '基础设置',
      description: '配置基本信息和偏好设置',
      clickable: true,
    },
    {
      id: 'tutorial2',
      title: '账户验证',
      description: '验证邮箱和手机号码',
      clickable: true,
    },
    {
      id: 'tutorial3',
      title: '完善资料',
      description: '上传头像和个人资料',
      clickable: true,
    },
    {
      id: 'tutorial4',
      title: '开始使用',
      description: '开始探索平台功能',
      clickable: true,
    },
  ];

  /**
   * 当前步骤索引状态
   */
  @state()
  private currentBasicStep = 1;

  @state()
  private currentClickableStep = 0;

  /**
   * 事件日志
   */
  @state()
  private eventLog: string[] = [];

  /**
   * 组件样式
   */
  static styles = css`
    :host {
      display: block;
      padding: 2rem;
      font-family:
        system-ui,
        -apple-system,
        sans-serif;
    }

    .demo-section {
      margin-bottom: 3rem;
      padding: 1.5rem;
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
      background: #fafafa;
    }

    .demo-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: #374151;
    }

    .demo-description {
      font-size: 0.875rem;
      color: #6b7280;
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }

    .demo-controls {
      margin: 1rem 0;
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
    }

    .demo-button {
      padding: 0.5rem 1rem;
      border: 1px solid #d1d5db;
      border-radius: 0.375rem;
      background: white;
      color: #374151;
      font-size: 0.875rem;
      cursor: pointer;
      transition: all 0.2s;
    }

    .demo-button:hover {
      background: #f3f4f6;
      border-color: #9ca3af;
    }

    .demo-button:active {
      background: #e5e7eb;
    }

    .demo-button.primary {
      background: #3b82f6;
      color: white;
      border-color: #2563eb;
    }

    .demo-button.primary:hover {
      background: #2563eb;
    }

    .event-log {
      margin-top: 1rem;
      padding: 1rem;
      background: #1f2937;
      color: #f9fafb;
      border-radius: 0.375rem;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 0.75rem;
      max-height: 200px;
      overflow-y: auto;
    }

    .event-log-item {
      margin-bottom: 0.25rem;
      padding: 0.25rem 0;
      border-bottom: 1px solid #374151;
    }

    .event-log-item:last-child {
      border-bottom: none;
    }

    .layout-demo {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      margin-top: 1rem;
    }

    @media (max-width: 768px) {
      .layout-demo {
        grid-template-columns: 1fr;
      }
    }

    .layout-item {
      padding: 1rem;
      border: 1px solid #e5e7eb;
      border-radius: 0.375rem;
      background: white;
    }

    .layout-title {
      font-weight: 600;
      margin-bottom: 1rem;
      color: #374151;
    }
  `;

  /**
   * 处理步骤点击事件
   */
  private handleStepClick(event: CustomEvent<StepClickEvent>): void {
    const { step, index, previousIndex } = event.detail;

    this.addEventLog(`步骤点击: "${step.title}" (索引: ${index}, 前一个: ${previousIndex})`);

    // 更新当前步骤
    this.currentClickableStep = index;
  }

  /**
   * 添加事件日志
   */
  private addEventLog(message: string): void {
    const timestamp = new Date().toLocaleTimeString();
    this.eventLog = [`[${timestamp}] ${message}`, ...this.eventLog.slice(0, 9)];
  }

  /**
   * 控制基础演示的步骤
   */
  private nextBasicStep(): void {
    if (this.currentBasicStep < this.basicSteps.length - 1) {
      this.currentBasicStep++;
      this.addEventLog(`基础演示前进到步骤 ${this.currentBasicStep + 1}`);
    }
  }

  private prevBasicStep(): void {
    if (this.currentBasicStep > 0) {
      this.currentBasicStep--;
      this.addEventLog(`基础演示后退到步骤 ${this.currentBasicStep + 1}`);
    }
  }

  private resetBasicStep(): void {
    this.currentBasicStep = 0;
    this.addEventLog('基础演示重置到第一步');
  }

  /**
   * 清空事件日志
   */
  private clearEventLog(): void {
    this.eventLog = [];
  }

  /**
   * 渲染基础演示
   */
  private renderBasicDemo(): any {
    return html`
      <div class="demo-section">
        <h3 class="demo-title">基础用法演示</h3>
        <p class="demo-description">
          展示基本的流程步骤，支持程序控制当前步骤。步骤状态根据 currentStep 属性自动计算。
        </p>

        <process-steps
          .steps="${this.basicSteps}"
          .currentStep="${this.currentBasicStep}"
          orientation="horizontal"
          show-description
          show-connector
        ></process-steps>

        <div class="demo-controls">
          <button
            class="demo-button"
            @click="${this.prevBasicStep}"
            ?disabled="${this.currentBasicStep === 0}"
          >
            上一步
          </button>
          <button
            class="demo-button primary"
            @click="${this.nextBasicStep}"
            ?disabled="${this.currentBasicStep === this.basicSteps.length - 1}"
          >
            下一步
          </button>
          <button class="demo-button" @click="${this.resetBasicStep}">重置</button>
        </div>
      </div>
    `;
  }

  /**
   * 渲染自定义状态演示
   */
  private renderCustomStatusDemo(): any {
    return html`
      <div class="demo-section">
        <h3 class="demo-title">自定义状态演示</h3>
        <p class="demo-description">
          每个步骤可以单独指定状态，不依赖于 currentStep 的自动计算。适用于复杂的业务流程。
        </p>

        <process-steps
          .steps="${this.customStatusSteps}"
          orientation="horizontal"
          show-description
          show-connector
        ></process-steps>
      </div>
    `;
  }

  /**
   * 渲染交互演示
   */
  private renderInteractiveDemo(): any {
    return html`
      <div class="demo-section">
        <h3 class="demo-title">交互式演示</h3>
        <p class="demo-description">
          启用 allow-click 属性后，用户可以点击步骤进行跳转。支持键盘导航和自定义事件。
        </p>

        <process-steps
          .steps="${this.clickableSteps}"
          .currentStep="${this.currentClickableStep}"
          orientation="horizontal"
          show-description
          show-connector
          allow-click
          @step-click="${this.handleStepClick}"
        ></process-steps>

        <div class="demo-controls">
          <button class="demo-button" @click="${this.clearEventLog}">清空日志</button>
        </div>

        ${this.eventLog.length > 0
          ? html`
              <div class="event-log">
                <div><strong>事件日志:</strong></div>
                ${this.eventLog.map(log => html` <div class="event-log-item">${log}</div> `)}
              </div>
            `
          : ''}
      </div>
    `;
  }

  /**
   * 渲染布局演示
   */
  private renderLayoutDemo(): any {
    return html`
      <div class="demo-section">
        <h3 class="demo-title">布局演示</h3>
        <p class="demo-description">支持水平和垂直两种布局方向，适应不同的界面设计需求。</p>

        <div class="layout-demo">
          <div class="layout-item">
            <div class="layout-title">水平布局</div>
            <process-steps
              .steps="${this.basicSteps.slice(0, 3)}"
              .currentStep="${1}"
              orientation="horizontal"
              show-description
              show-connector
            ></process-steps>
          </div>

          <div class="layout-item">
            <div class="layout-title">垂直布局</div>
            <process-steps
              .steps="${this.basicSteps.slice(0, 3)}"
              .currentStep="${1}"
              orientation="vertical"
              show-description
              show-connector
            ></process-steps>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 主渲染方法
   */
  protected render(): any {
    return html`
      <div>
        <h1>ProcessSteps 组件演示</h1>
        <p>一个功能完整的流程步骤展示组件，支持多种配置和交互方式。</p>

        ${this.renderBasicDemo()} ${this.renderCustomStatusDemo()} ${this.renderInteractiveDemo()}
        ${this.renderLayoutDemo()}
      </div>
    `;
  }
}

// 声明自定义元素的类型
declare global {
  interface HTMLElementTagNameMap {
    'process-steps-demo': ProcessStepsDemo;
  }
}
