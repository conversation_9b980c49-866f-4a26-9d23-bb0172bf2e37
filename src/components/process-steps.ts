import { LitElement, html, css, PropertyValues } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { repeat } from 'lit/directives/repeat.js';

/**
 * 步骤状态枚举
 */
export enum StepStatus {
  PENDING = 'pending', // 未开始
  PROCESSING = 'processing', // 进行中
  COMPLETED = 'completed', // 已完成
}

/**
 * 单个步骤的数据接口
 */
export interface ProcessStep {
  id: string;
  title: string;
  description?: string;
  status?: StepStatus;
  timestamp?: Date | string;
  icon?: string;
  clickable?: boolean;
}

/**
 * 步骤点击事件详情
 */
export interface StepClickEvent {
  step: ProcessStep;
  index: number;
  previousIndex: number;
}

/**
 * 流程步骤展示组件
 * 支持水平和垂直布局，可配置样式和交互行为
 */
@customElement('process-steps')
export class ProcessSteps extends LitElement {
  /**
   * 步骤数据数组
   */
  @property({ type: Array })
  steps: ProcessStep[] = [];

  /**
   * 当前激活的步骤索引
   */
  @property({ type: Number, attribute: 'current-step' })
  currentStep: number = 0;

  /**
   * 布局方向：horizontal | vertical
   */
  @property({ type: String })
  orientation: 'horizontal' | 'vertical' = 'horizontal';

  /**
   * 是否显示步骤描述
   */
  @property({ type: Boolean, attribute: 'show-description' })
  showDescription: boolean = true;

  /**
   * 是否允许点击跳转
   */
  @property({ type: Boolean, attribute: 'allow-click' })
  allowClick: boolean = false;

  /**
   * 是否显示连接线
   */
  @property({ type: Boolean, attribute: 'show-connector' })
  showConnector: boolean = true;

  /**
   * 自定义主题色
   */
  @property({ type: String, attribute: 'theme-color' })
  themeColor: string = 'blue';

  /**
   * 内部状态：处理后的步骤数据
   */
  @state()
  private processedSteps: (ProcessStep & { computedStatus: StepStatus })[] = [];

  /**
   * 组件样式定义
   */
  static styles = css`
    :host {
      display: block;
      font-family:
        system-ui,
        -apple-system,
        sans-serif;
    }

    .process-container {
      display: flex;
      gap: 1rem;
    }

    .process-container.horizontal {
      flex-direction: row;
      align-items: flex-start;
    }

    .process-container.vertical {
      flex-direction: column;
      align-items: stretch;
    }

    .step-item {
      display: flex;
      position: relative;
      transition: all 0.3s ease;
    }

    .step-item.horizontal {
      flex-direction: column;
      align-items: center;
      text-align: center;
      flex: 1;
    }

    .step-item.vertical {
      flex-direction: row;
      align-items: flex-start;
      text-align: left;
    }

    .step-item.clickable {
      cursor: pointer;
    }

    .step-item.clickable:hover .step-icon {
      transform: scale(1.1);
    }

    .step-icon {
      width: 2.5rem;
      height: 2.5rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 0.875rem;
      transition: all 0.3s ease;
      position: relative;
      z-index: 2;
    }

    .step-icon.pending {
      background-color: #e5e7eb;
      color: #6b7280;
      border: 2px solid #d1d5db;
    }

    .step-icon.processing {
      background-color: #3b82f6;
      color: white;
      border: 2px solid #2563eb;
      animation: pulse 2s infinite;
    }

    .step-icon.completed {
      background-color: #10b981;
      color: white;
      border: 2px solid #059669;
    }

    @keyframes pulse {
      0%,
      100% {
        opacity: 1;
      }
      50% {
        opacity: 0.7;
      }
    }

    .step-content {
      margin-top: 0.5rem;
    }

    .step-content.vertical {
      margin-top: 0;
      margin-left: 1rem;
      flex: 1;
    }

    .step-title {
      font-weight: 600;
      font-size: 0.875rem;
      margin-bottom: 0.25rem;
      color: #374151;
    }

    .step-description {
      font-size: 0.75rem;
      color: #6b7280;
      line-height: 1.4;
    }

    .step-connector {
      position: absolute;
      background-color: #e5e7eb;
      z-index: 1;
    }

    .step-connector.horizontal {
      height: 2px;
      top: 1.25rem;
      left: 2.5rem;
      right: -1rem;
    }

    .step-connector.vertical {
      width: 2px;
      left: 1.25rem;
      top: 2.5rem;
      bottom: -1rem;
    }

    .step-connector.active {
      background-color: #10b981;
    }

    .step-timestamp {
      font-size: 0.625rem;
      color: #9ca3af;
      margin-top: 0.25rem;
    }
  `;

  /**
   * 属性更新时重新计算步骤状态
   */
  protected willUpdate(changedProperties: PropertyValues): void {
    if (changedProperties.has('steps') || changedProperties.has('currentStep')) {
      this.computeStepStatuses();
    }
  }

  /**
   * 计算每个步骤的状态
   */
  private computeStepStatuses(): void {
    this.processedSteps = this.steps.map((step, index) => {
      let computedStatus: StepStatus;

      if (step.status) {
        // 如果步骤已指定状态，使用指定状态
        computedStatus = step.status;
      } else {
        // 根据当前步骤索引自动计算状态
        if (index < this.currentStep) {
          computedStatus = StepStatus.COMPLETED;
        } else if (index === this.currentStep) {
          computedStatus = StepStatus.PROCESSING;
        } else {
          computedStatus = StepStatus.PENDING;
        }
      }

      return {
        ...step,
        computedStatus,
      };
    });
  }

  /**
   * 处理步骤点击事件
   */
  private handleStepClick(step: ProcessStep, index: number): void {
    if (!this.allowClick || !step.clickable) return;

    const previousIndex = this.currentStep;

    // 派发自定义事件
    const event = new CustomEvent<StepClickEvent>('step-click', {
      detail: {
        step,
        index,
        previousIndex,
      },
      bubbles: true,
      composed: true,
    });

    this.dispatchEvent(event);

    // 如果事件没有被阻止，更新当前步骤
    if (!event.defaultPrevented) {
      this.currentStep = index;
    }
  }

  /**
   * 处理键盘导航
   */
  private handleKeyDown(event: KeyboardEvent, step: ProcessStep, index: number): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.handleStepClick(step, index);
    }
  }

  /**
   * 格式化时间戳显示
   */
  private formatTimestamp(timestamp?: Date | string): string {
    if (!timestamp) return '';

    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  /**
   * 渲染步骤图标
   */
  private renderStepIcon(step: ProcessStep & { computedStatus: StepStatus }, index: number): any {
    const iconClasses = {
      'step-icon': true,
      [step.computedStatus]: true,
    };

    let iconContent;
    if (step.icon) {
      iconContent = html`<i class="${step.icon}"></i>`;
    } else {
      switch (step.computedStatus) {
        case StepStatus.COMPLETED:
          iconContent = html`✓`;
          break;
        case StepStatus.PROCESSING:
          iconContent = html`${index + 1}`;
          break;
        default:
          iconContent = html`${index + 1}`;
      }
    }

    return html` <div class="${classMap(iconClasses)}">${iconContent}</div> `;
  }

  /**
   * 渲染步骤连接线
   */
  private renderConnector(index: number): any {
    if (!this.showConnector || index === this.processedSteps.length - 1) {
      return '';
    }

    const isActive = index < this.currentStep;
    const connectorClasses = {
      'step-connector': true,
      [this.orientation]: true,
      active: isActive,
    };

    return html`<div class="${classMap(connectorClasses)}"></div>`;
  }

  /**
   * 渲染单个步骤
   */
  private renderStep(step: ProcessStep & { computedStatus: StepStatus }, index: number): any {
    const stepClasses = {
      'step-item': true,
      [this.orientation]: true,
      clickable: this.allowClick && step.clickable !== false,
    };

    const contentClasses = {
      'step-content': true,
      [this.orientation]: true,
    };

    return html`
      <div
        class="${classMap(stepClasses)}"
        @click="${() => this.handleStepClick(step, index)}"
        @keydown="${(e: KeyboardEvent) => this.handleKeyDown(e, step, index)}"
        tabindex="${this.allowClick && step.clickable !== false ? '0' : '-1'}"
        role="button"
        aria-label="步骤 ${index + 1}: ${step.title}"
        aria-current="${index === this.currentStep ? 'step' : 'false'}"
      >
        ${this.renderStepIcon(step, index)} ${this.renderConnector(index)}

        <div class="${classMap(contentClasses)}">
          <div class="step-title">${step.title}</div>
          ${this.showDescription && step.description
            ? html`<div class="step-description">${step.description}</div>`
            : ''}
          ${step.timestamp
            ? html`<div class="step-timestamp">${this.formatTimestamp(step.timestamp)}</div>`
            : ''}
        </div>
      </div>
    `;
  }

  /**
   * 组件主渲染方法
   */
  protected render(): any {
    const containerClasses = {
      'process-container': true,
      [this.orientation]: true,
    };

    return html`
      <div class="${classMap(containerClasses)}" role="progressbar" aria-label="流程进度">
        ${repeat(
          this.processedSteps,
          step => step.id,
          (step, index) => this.renderStep(step, index)
        )}
      </div>
    `;
  }
}

// 声明自定义元素的类型
declare global {
  interface HTMLElementTagNameMap {
    'process-steps': ProcessSteps;
  }
}
