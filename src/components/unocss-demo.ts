import { LitElement, html, unsafeCSS } from 'lit'
import { customElement, property } from 'lit/decorators.js'

// 定义包含 UnoCSS 占位符的样式字符串
const componentStyles = `
  :host {
    display: block;
    font-family: system-ui, -apple-system, sans-serif;
  }
  @unocss-placeholder
`;

/**
 * UnoCSS 演示组件
 * 展示 UnoCSS 的各种功能和语法
 */
@customElement('unocss-demo')
export class UnoCSSDemo extends LitElement {
  /**
   * 演示文本
   */
  @property()
  demoText = 'UnoCSS Demo'

  /**
   * 是否显示高级功能
   */
  @property({ type: Boolean })
  showAdvanced = false

  /**
   * 渲染组件模板
   * @returns 模板结果
   */
  protected render() {
    return html`
      <div class="p-8 max-w-4xl mx-auto space-y-6">
        <!-- 标题区域 -->
        <div class="text-center">
          <h1 class="text-4xl font-bold text-blue-600 mb-2">${this.demoText}</h1>
          <p class="text-gray-600">展示 UnoCSS 的强大功能</p>
        </div>

        <!-- 基础样式演示 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div class="card bg-gradient-to-r from-blue-500 to-purple-600 text-white">
            <h3 class="text-xl font-semibold mb-2">渐变背景</h3>
            <p>UnoCSS 支持渐变背景</p>
          </div>
          
          <div class="card bg-white border-2 border-dashed border-gray-300 hover:border-blue-500 transition-colors">
            <h3 class="text-xl font-semibold mb-2 text-gray-800">悬停效果</h3>
            <p class="text-gray-600">鼠标悬停查看边框变化</p>
          </div>
          
          <div class="card bg-yellow-100 border-l-4 border-yellow-500">
            <h3 class="text-xl font-semibold mb-2 text-yellow-800">左边框</h3>
            <p class="text-yellow-700">警告样式卡片</p>
          </div>
        </div>

        <!-- 响应式演示 -->
        <div class="bg-gray-100 p-6 rounded-lg">
          <h2 class="text-2xl font-bold mb-4">响应式设计</h2>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-red-500 text-white p-4 rounded text-center">手机</div>
            <div class="bg-green-500 text-white p-4 rounded text-center hidden sm:block">平板+</div>
            <div class="bg-blue-500 text-white p-4 rounded text-center hidden lg:block">桌面+</div>
            <div class="bg-purple-500 text-white p-4 rounded text-center hidden lg:block">大屏+</div>
          </div>
        </div>

        <!-- 交互按钮 -->
        <div class="text-center space-x-4">
          <button 
            @click=${this._toggleAdvanced}
            class="btn"
          >
            ${this.showAdvanced ? '隐藏' : '显示'}高级功能
          </button>
          
          <button class="px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 active:scale-95 transform transition-all">
            动画按钮
          </button>
        </div>

        <!-- 高级功能演示 -->
        ${this.showAdvanced ? html`
          <div class="bg-white p-6 rounded-lg shadow-lg border">
            <h2 class="text-2xl font-bold mb-4">高级功能</h2>
            
            <!-- 属性化模式演示 -->
            <div 
              bg="gradient-to-r from-pink-500 to-violet-500"
              text="white center"
              p="6"
              rounded="lg"
              mb="4"
            >
              <h3 text="xl" font="semibold" mb="2">属性化模式</h3>
              <p>使用属性而不是类名来应用样式</p>
            </div>
            
            <!-- 自定义快捷方式 -->
            <div class="space-y-2">
              <div class="btn">自定义按钮快捷方式</div>
              <div class="card bg-blue-50">
                <p>自定义卡片快捷方式</p>
              </div>
            </div>
          </div>
        ` : ''}

        <!-- 工具类演示 -->
        <div class="bg-gray-50 p-6 rounded-lg">
          <h2 class="text-2xl font-bold mb-4">实用工具类</h2>
          <div class="space-y-4">
            <div class="flex items-center space-x-4">
              <div class="w-4 h-4 bg-red-500 rounded-full"></div>
              <span class="text-sm font-mono">w-4 h-4 bg-red-500 rounded-full</span>
            </div>
            <div class="flex items-center space-x-4">
              <div class="w-8 h-8 bg-blue-500 rounded-lg shadow-md"></div>
              <span class="text-sm font-mono">w-8 h-8 bg-blue-500 rounded-lg shadow-md</span>
            </div>
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-blue-500 rounded-xl"></div>
              <span class="text-sm font-mono">w-12 h-12 bg-gradient-to-br from-green-400 to-blue-500 rounded-xl</span>
            </div>
          </div>
        </div>
      </div>
    `
  }

  /**
   * 切换高级功能显示状态
   */
  private _toggleAdvanced() {
    this.showAdvanced = !this.showAdvanced
  }

  /**
   * 组件样式
   * 使用 unsafeCSS 和 @unocss-placeholder 来注入 UnoCSS 样式
   */
  static styles = [unsafeCSS(componentStyles)]
}

declare global {
  interface HTMLElementTagNameMap {
    'unocss-demo': UnoCSSDemo
  }
}