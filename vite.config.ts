import {defineConfig} from 'vite'
import UnoCSS from 'unocss/vite'
import {resolve} from 'path';
import {presetAttributify, presetIcons, presetTypography, presetWind3} from "unocss";
import ViteInspector from 'vite-plugin-inspect';
 
const unocssConfig = {
            shortcuts: {
                logo: 'i-logos-webcomponents w-6em h-6em transform transition-800 hover:rotate-180',
                'cool-blue': 'bg-blue-500 text-white',
                'cool-green': 'bg-green-500 text-black',
            },
            presets: [
                presetWind3(),
                presetAttributify(),
                presetIcons({
                    extraProperties: {
                        'display': 'inline-block',
                        'vertical-align': 'middle',
                    },
                }),
                presetTypography(),
            ],
            inspector: false,
        };

export default defineConfig({
    plugins: [
        UnoCSS({
            mode: 'shadow-dom',
            ...unocssConfig,
        }),
        ViteInspector(),
    ],
    build: {
        lib: {
            entry: resolve(__dirname, 'src/index.ts'),
            name: 'litcdc-rcf',
            formats: ['es', 'umd'],
            fileName: (format) => `litcdc-rcf.${format}.js`,
        },
    },
    resolve: {
        alias: {
            '@': resolve(__dirname, 'src'),
        },
    },
    server: {
        port: 3000,
        open: true,
    },
})