import { defineConfig } from 'vite';
import UnoCSS from 'unocss/vite';
import { resolve } from 'path';
import { presetAttributify, presetIcons, presetTypography, presetWind3 } from 'unocss';
import ViteInspector from 'vite-plugin-inspect';

// 检查是否为生产环境
const isProduction = process.env.NODE_ENV === 'production';

const unocssConfig = {
  shortcuts: {
    logo: 'w-6em h-6em transform transition-800 hover:rotate-180 text-blue-500',
    'cool-blue': 'bg-blue-500 text-white',
    'cool-green': 'bg-green-500 text-black',
    // 添加更多常用快捷方式
    btn: 'px-4 py-2 rounded-lg font-medium transition-colors',
    'btn-primary': 'btn bg-blue-500 hover:bg-blue-600 text-white',
    'btn-secondary': 'btn bg-gray-200 hover:bg-gray-300 text-gray-800',
    card: 'bg-white rounded-lg shadow-md p-6',
  },
  presets: [
    presetWind3(),
    presetAttributify(),
    presetIcons({
      extraProperties: {
        display: 'inline-block',
        'vertical-align': 'middle',
      },
    }),
    presetTypography(),
  ],
  // 生产环境禁用 inspector
  inspector: !isProduction,
  // 添加 safelist 确保关键样式不被误删
  safelist: [
    // 基础布局类
    'block',
    'inline-block',
    'flex',
    'grid',
    'hidden',
    // 常用间距
    'p-4',
    'p-6',
    'p-8',
    'm-4',
    'm-6',
    'm-8',
    'px-4',
    'py-2',
    'px-6',
    'py-4',
    // 常用颜色
    'text-white',
    'text-black',
    'text-gray-500',
    'text-gray-600',
    'bg-white',
    'bg-gray-50',
    'bg-gray-100',
    'bg-blue-500',
    'bg-green-500',
    'bg-red-500',
    'bg-yellow-500',
    // 常用尺寸
    'w-full',
    'h-full',
    'w-auto',
    'h-auto',
    'w-12',
    'h-12',
    'w-16',
    'h-16',
    // 圆角和阴影
    'rounded',
    'rounded-lg',
    'rounded-xl',
    'shadow',
    'shadow-md',
    'shadow-lg',
    'shadow-xl',
    // 过渡效果
    'transition',
    'transition-colors',
    'transition-transform',
    // 响应式断点相关
    'sm:block',
    'md:grid-cols-3',
    'lg:grid-cols-4',
    // 自定义快捷方式
    'logo',
    'cool-blue',
    'cool-green',
    'btn',
    'btn-primary',
    'btn-secondary',
    'card',
    // ProcessSteps 组件相关样式
    'flex-row',
    'flex-col',
    'items-center',
    'items-start',
    'justify-center',
    'relative',
    'absolute',
    'z-1',
    'z-2',
    'cursor-pointer',
    'border-2',
    'border-solid',
    'border-gray-300',
    'border-blue-500',
    'border-green-500',
    'bg-gray-200',
    'bg-blue-500',
    'bg-green-500',
    'text-gray-600',
    'text-white',
    'font-bold',
    'font-semibold',
    'text-sm',
    'text-xs',
    'animate-pulse',
    'hover:scale-110',
    'transition-all',
    'duration-300',
    'grid',
    'grid-cols-1',
    'grid-cols-2',
    'gap-4',
    'gap-8',
  ],
  // 启用 extractorSplit 配置以提高性能
  extractorSplit: true,
  // 配置提取器选项
  extractors: [
    {
      name: 'lit-extractor',
      // 针对 TypeScript/JavaScript 文件的提取器
      extensions: ['ts', 'js'],
      extractor: content => {
        // 提取类名，包括动态类名
        const matches = content.match(/class[Name]*\s*[:=]\s*["'`]([^"'`]*)["'`]/g) || [];
        const classNames = matches
          .map(match => match.replace(/class[Name]*\s*[:=]\s*["'`]([^"'`]*)["'`]/, '$1'))
          .join(' ');

        // 也提取模板字符串中的类名
        const templateMatches = content.match(/class\s*=\s*["']([^"']*)["']/g) || [];
        const templateClassNames = templateMatches
          .map(match => match.replace(/class\s*=\s*["']([^"']*)["']/, '$1'))
          .join(' ');

        return (classNames + ' ' + templateClassNames).split(/\s+/).filter(Boolean);
      },
    },
  ],
};

export default defineConfig({
  plugins: [
    UnoCSS({
      mode: 'shadow-dom',
      ...unocssConfig,
    }),
    // 生产环境禁用 ViteInspector
    ...(!isProduction ? [ViteInspector()] : []),
  ],
  esbuild: {
    drop: ['console', 'debugger'],
  },
  build: {
    minify: 'esbuild',
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'litcdc-rcf',
      formats: ['es', 'umd'],
      fileName: format => `litcdc-rcf.${format}.js`,
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 3000,
    open: true,
  },
});
